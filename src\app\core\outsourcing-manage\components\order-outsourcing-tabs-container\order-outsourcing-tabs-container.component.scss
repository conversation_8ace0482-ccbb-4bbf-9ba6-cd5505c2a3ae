$tabsBorderColor: #d4d7dc;
$tabsActiveBorderColor: #138aff;
$tabActiveTextColor: #007aff;
$tabActiveBgColor: #eef7ff;

.order-tabs-container {
  .order-tabs-header {
    display: block;
    margin-top: -7px;

    // 为了能使右上角的x 完整的显示 多加了padding，多余的padding 需要在 .order-tabs-header规则中添加 等值的负margin
    & ::ng-deep .ant-tabs-nav-list {
      padding-top: 7px;
    }

    & ::ng-deep .ant-tabs-top > .ant-tabs-nav {
      margin: 0;
    }

    & ::ng-deep .ant-tabs-tab {
      height: 40px;
      padding: 2px 10px;
      background-color: #f7f8fa;
      border-left: 1px solid $tabsBorderColor;
      border-right: 1px solid $tabsBorderColor;
      border-top: 1px solid $tabsBorderColor;
      border-radius: 4px 4px 0px 0px;

      &:not(:last-child) {
        margin-right: 8px;
      }
    }

    // 右上角 x 盒子
    & ::ng-deep .ant-tabs-tab-remove {
      font-size: 8px;
      padding: 0;
      margin: 0;

      &:hover {
        opacity: 0.8;
      }
    }

    // 右上角 x
    & ::ng-deep .anticon-close {
      margin: 0;
      position: absolute;
      top: 0;
      right: 0;
      border-radius: 50%;
      background-color: #222b3c;
      opacity: 0.35;
      color: #fff;
      transform: translate(50%, -50%);
      padding: 2px;

      &:hover {
        background-color: #000000;
        opacity: 0.7;
      }
    }

    .red-dot {
      height: 6px;
      width: 6px;
      background: #f96d6d;
      position: absolute;
      right: -2px;
      top: -2px;
      border-radius: 50%;
    }

    &::ng-deep .ant-tabs-nav-list {
      padding-top: 8px;
    }

    .order-tabs-header-item {
      display: inline-flex;
      flex-direction: column;
      justify-content: center;

      & span:first-child {
        color: #515665;
        font-size: 16px;
        font-weight: 500;
        line-height: 20px;
      }

      & span:last-child {
        color: #515665;
        font-size: 12px;
        font-weight: 400;
        line-height: 14px;
      }

      .select-po {
        min-width: 100px;
      }
    }

    // 三个点
    & ::ng-deep .ant-tabs-nav-more {
      padding: 8px;
    }

    & ::ng-deep .ant-tabs-nav-operations {
      flex-shrink: 0;
    }

    // 下拉选
    .order-tabs-header-roll {
      nz-select {
        width: 140px;
      }
    }

    // tab选中状态
    & ::ng-deep .ant-tabs-tab-active,
    & ::ng-deep .ant-tabs-tab:hover {
      background: $tabActiveBgColor;
      border-radius: 4px 4px 0px 0px;
      border-top: 1px solid $tabsActiveBorderColor;
      border-left: 1px solid $tabsActiveBorderColor;
      border-right: 1px solid $tabsActiveBorderColor;
      border-bottom: 1px solid $tabActiveBgColor;

      & .order-tabs-header-item {
        & span:first-child {
          color: $tabActiveTextColor !important;
        }

        & span:last-child {
          color: $tabActiveTextColor !important;
        }
      }
    }
  }

  .order-tabs-body {
    border: 1px solid #d4d7dc;
    background-color: #f0f3fa;
    border-radius: 0px 4px 4px 4px;
    padding: 6px;

    .order-tabs-body-banner {
      padding: 8px 12px;
      background: #f9fafb;
      border-radius: 4px 4px 0px 0px;
      height: 44px;
      display: flex;
      align-items: center;

      .order-tabs-body-banner-item {
        display: inline-flex;
        align-items: center;

        &:not(:last-child) {
          &::after {
            content: '';
            margin: 0 16px;
            display: inline-block;
            height: 16px;
            width: 1px;
            background-color: $tabsBorderColor;
          }
        }

        // 带数字的文本域样式重置
        .inline-count {
          position: relative;
          &::after {
            position: absolute;
            right: 8px;
            bottom: 0px;
            font-size: 12px;
          }
        }
      }
    }
  }
}

//
::ng-deep .ant-dropdown-menu {
  .ant-tabs-dropdown-menu-item {
    .order-tabs-header-item {
      width: 100%;
      display: inline-flex;
      flex-direction: column;
      justify-content: center;

      & span:first-child {
        color: #515665;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
      }

      & span:last-child {
        color: #515665;
        font-size: 12px;
        font-weight: 400;
        line-height: 14px;
      }

      &:hover {
        span {
          color: $tabActiveTextColor;
        }
      }
    }
  }
}

// 收货地址
.personal-info {
  background-color: #fff;
  border-radius: 0 0 4px 4px;
  padding: 0 8px 8px 8px;
  display: flex;

  & > div {
    display: flex;
    flex-wrap: nowrap;
    height: 32px;
    align-items: center;
    min-width: 250px;
    margin-right: 24px;

    & > span {
      flex-shrink: 0;
      color: #54607c;
    }
  }
}

// 日期选择器容器样式
.date-picker-container {
  position: relative;
  z-index: 1;
}

// 全局样式：强制修复日期选择器弹出层定位问题
:host ::ng-deep {
  // 确保日期选择器本身的样式正常
  .date-picker-container {
    .ant-picker {
      position: relative;
      z-index: 1;
    }
  }
}

// 全局样式：修复所有日期选择器弹出层的定位问题
::ng-deep {
  .ant-picker-dropdown {
    position: fixed !important;
    z-index: 9999 !important;
    transform: none !important;

    // 确保弹出层不会被父容器的transform影响
    &.ant-slide-up-enter,
    &.ant-slide-up-appear {
      transform: none !important;
    }
  }

  .ant-picker-panel-container {
    position: relative !important;
    transform: none !important;
  }

  // 修复可能的transform问题
  .ant-picker-dropdown-placement-bottomLeft {
    transform: none !important;
  }

  // 确保弹出层内容正常显示
  .ant-picker-panel {
    position: relative;
    z-index: 1;
  }
}
